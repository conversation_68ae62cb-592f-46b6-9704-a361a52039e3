from django.urls import path
from . import views

urlpatterns = [
    # Student Authentication
    path('', views.student_login, name='student_login'),
    path('login/', views.student_login, name='student_login'),
    path('signup/', views.student_signup, name='student_signup'),
    path('logout/', views.student_logout, name='student_logout'),

    # Student Dashboard
    path('dashboard/', views.student_dashboard, name='student_dashboard'),
    path('enroll-face/', views.student_face_enrollment, name='student_face_enrollment'),
    path('mark-attendance/<int:class_id>/', views.student_mark_attendance, name='student_mark_attendance'),

    # Admin URLs (for staff/instructors)
    path('admin-dashboard/', views.dashboard, name='admin_dashboard'),
    path('students/', views.student_list, name='student_list'),
    path('students/register/', views.student_register, name='student_register'),
    path('students/<str:student_id>/', views.student_detail, name='student_detail'),
    path('students/<str:student_id>/enroll-face/', views.face_enrollment, name='face_enrollment'),

    # Class URLs
    path('classes/', views.class_list, name='class_list'),
    path('classes/<int:class_id>/', views.class_detail, name='class_detail'),
    path('classes/<int:class_id>/attendance/', views.take_attendance, name='take_attendance'),
    path('classes/<int:class_id>/history/', views.attendance_history, name='class_attendance_history'),

    # Attendance URLs
    path('attendance/history/', views.attendance_history, name='attendance_history'),

    # API URLs
    path('api/recognize-face/', views.api_recognize_face, name='api_recognize_face'),
    path('api/validate-image/', views.api_validate_image, name='api_validate_image'),
    path('api/attendance-status/<int:class_id>/', views.api_attendance_status, name='api_attendance_status'),
]
