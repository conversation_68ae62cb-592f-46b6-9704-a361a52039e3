"""
Simple DeepFace service using file-based approach
Following the recommended DeepFace workflow
"""

import os
import json
import base64
import logging
from io import BytesIO
from PIL import Image
from django.conf import settings
from django.core.files.base import ContentFile

logger = logging.getLogger(__name__)

# Try to import DeepFace
try:
    from deepface import DeepFace
    DEEPFACE_AVAILABLE = True
    print("✅ DeepFace imported successfully")
except ImportError:
    DEEPFACE_AVAILABLE = False
    print("❌ DeepFace not available")


class DeepFaceService:
    """Simple DeepFace service using file-based approach"""
    
    def __init__(self):
        self.available = DEEPFACE_AVAILABLE
        self.face_db_path = os.path.join(settings.MEDIA_ROOT, 'face_db')
        
        # Create face database directory
        os.makedirs(self.face_db_path, exist_ok=True)
        
        if self.available:
            self.model_name = 'VGG-Face'  # Can be changed to 'Facenet', 'OpenFace', etc.
            print(f"✅ DeepFace service initialized with {self.model_name}")
        else:
            print("❌ DeepFace service not available")
    
    def preprocess_image(self, image_data):
        """Convert base64 image data to PIL Image"""
        try:
            if isinstance(image_data, str):
                # Handle base64 encoded image
                if image_data.startswith('data:image'):
                    image_data = image_data.split(',')[1]
                
                image_bytes = base64.b64decode(image_data)
                image = Image.open(BytesIO(image_bytes))
            else:
                # Handle file upload
                image = Image.open(image_data)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            return image
        except Exception as e:
            logger.error(f"Error preprocessing image: {str(e)}")
            raise ValueError("Invalid image data")
    
    def enroll_student_face(self, student, image_data, is_primary=False):
        """
        Enroll student face using DeepFace file-based approach
        """
        try:
            # Import here to avoid circular imports
            from .models import FaceEncoding
            
            print(f"🎯 Enrolling face for student: {student.student_id}")
            
            # Preprocess image
            image = self.preprocess_image(image_data)
            print("✅ Image preprocessed successfully")
            
            # Create student directory in face_db
            student_face_dir = os.path.join(self.face_db_path, student.student_id)
            os.makedirs(student_face_dir, exist_ok=True)
            print(f"✅ Created directory: {student_face_dir}")
            
            # Save image to buffer
            img_buffer = BytesIO()
            image.save(img_buffer, format='JPEG')
            img_content = ContentFile(img_buffer.getvalue())
            
            # Create FaceEncoding record
            face_encoding = FaceEncoding.objects.create(
                student=student,
                encoding_data="file_based",  # We use file-based approach
                is_primary=is_primary
            )
            print(f"✅ Created FaceEncoding record: {face_encoding.id}")
            
            # Save image to Django media
            filename = f"{student.student_id}_{face_encoding.id}.jpg"
            face_encoding.image.save(filename, img_content)
            print(f"✅ Saved to Django media: {filename}")
            
            # Save image to face_db directory for DeepFace
            face_db_filename = f"face_{face_encoding.id}.jpg"
            face_db_filepath = os.path.join(student_face_dir, face_db_filename)
            
            # Reset buffer position
            img_buffer.seek(0)
            with open(face_db_filepath, 'wb') as f:
                f.write(img_buffer.getvalue())
            
            print(f"✅ Saved to face_db: {face_db_filepath}")
            
            # Test if DeepFace can read the image
            if self.available:
                try:
                    # Try to analyze the saved image
                    analysis = DeepFace.analyze(img_path=face_db_filepath, actions=['age'], enforce_detection=False)
                    print(f"✅ DeepFace can read the image successfully")
                except Exception as e:
                    print(f"⚠️  DeepFace analysis warning: {str(e)}")
            
            return face_encoding
            
        except Exception as e:
            logger.error(f"Error enrolling student face: {str(e)}")
            print(f"❌ Error enrolling face: {str(e)}")
            raise ValueError(f"Could not enroll face: {str(e)}")
    
    def recognize_student(self, image_data):
        """
        Recognize student using DeepFace.find()
        """
        if not self.available:
            print("❌ DeepFace not available for recognition")
            return None, 0
        
        try:
            print("🔍 Starting face recognition...")
            
            # Preprocess image
            image = self.preprocess_image(image_data)
            
            # Save temporary image for recognition
            temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp')
            os.makedirs(temp_dir, exist_ok=True)
            
            temp_image_path = os.path.join(temp_dir, 'recognition_temp.jpg')
            image.save(temp_image_path, 'JPEG')
            print(f"✅ Saved temp image: {temp_image_path}")
            
            # Use DeepFace.find to search in face database
            print(f"🔍 Searching in face database: {self.face_db_path}")
            
            results = DeepFace.find(
                img_path=temp_image_path,
                db_path=self.face_db_path,
                model_name=self.model_name,
                enforce_detection=False
            )
            
            print(f"✅ DeepFace.find completed, results: {len(results)} dataframes")
            
            # Clean up temp file
            try:
                os.remove(temp_image_path)
            except:
                pass
            
            # Process results
            if len(results) > 0 and not results[0].empty:
                # Get the best match (lowest distance)
                best_match = results[0].iloc[0]
                identity_path = best_match['identity']
                distance = best_match['distance']
                
                print(f"✅ Best match: {identity_path}, distance: {distance}")
                
                # Extract student ID from path
                # Path format: face_db/STUDENT_ID/face_X.jpg
                path_parts = identity_path.replace('\\', '/').split('/')
                if len(path_parts) >= 2:
                    student_id = path_parts[-2]  # Get student ID from directory name
                    
                    # Find student
                    from .models import Student
                    try:
                        student = Student.objects.get(student_id=student_id)
                        confidence = max(0, 1 - (distance / 1.0))  # Convert distance to confidence
                        
                        print(f"✅ Recognized student: {student.full_name} (confidence: {confidence:.2f})")
                        return student, confidence
                    except Student.DoesNotExist:
                        print(f"❌ Student {student_id} not found in database")
                        return None, 0
                else:
                    print(f"❌ Could not extract student ID from path: {identity_path}")
                    return None, 0
            else:
                print("❌ No matches found")
                return None, 0
                
        except Exception as e:
            logger.error(f"Error recognizing student: {str(e)}")
            print(f"❌ Recognition error: {str(e)}")
            return None, 0
    
    def validate_image_quality(self, image_data):
        """Basic image validation"""
        try:
            image = self.preprocess_image(image_data)
            
            # Check image size
            width, height = image.size
            if width < 100 or height < 100:
                return False, "Image too small (minimum 100x100 pixels)"
            
            return True, "Image quality is acceptable"
            
        except Exception as e:
            return False, f"Error validating image: {str(e)}"


# Global instance
deepface_service = DeepFaceService()
