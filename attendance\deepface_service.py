"""
Simple DeepFace service using file-based approach
Following the recommended DeepFace workflow
"""

import os
import json
import base64
import logging
from io import BytesIO
from PIL import Image
from django.conf import settings
from django.core.files.base import ContentFile

logger = logging.getLogger(__name__)

# Try to import DeepFace and preload model
try:
    from deepface import DeepFace
    DEEPFACE_AVAILABLE = True
    print("✅ DeepFace imported successfully")

    # Model options (from fastest to most accurate)
    AVAILABLE_MODELS = {
        'SFace': {'speed': 'fastest', 'accuracy': 'good', 'size': 'small'},
        'Facenet': {'speed': 'fast', 'accuracy': 'very good', 'size': 'medium'},
        'ArcFace': {'speed': 'medium', 'accuracy': 'excellent', 'size': 'medium'},
        'VGG-Face': {'speed': 'slow', 'accuracy': 'excellent', 'size': 'large'},
        'OpenFace': {'speed': 'medium', 'accuracy': 'good', 'size': 'small'},
    }

    # Try to preload models (start with faster ones)
    PRELOADED_MODELS = {}
    MODEL_PRELOADED = False

    # Try to preload the default model
    DEFAULT_MODEL = 'Facenet'  # Good balance of speed and accuracy

    try:
        if DEFAULT_MODEL == 'VGG-Face':
            from deepface.basemodels import VGGFace
            PRELOADED_MODELS['VGG-Face'] = VGGFace.loadModel()
        elif DEFAULT_MODEL == 'Facenet':
            from deepface.basemodels import Facenet
            PRELOADED_MODELS['Facenet'] = Facenet.loadModel()
        elif DEFAULT_MODEL == 'ArcFace':
            from deepface.basemodels import ArcFace
            PRELOADED_MODELS['ArcFace'] = ArcFace.loadModel()
        elif DEFAULT_MODEL == 'OpenFace':
            from deepface.basemodels import OpenFace
            PRELOADED_MODELS['OpenFace'] = OpenFace.loadModel()
        # SFace doesn't have a separate basemodel import

        print(f"✅ {DEFAULT_MODEL} model preloaded successfully")
        MODEL_PRELOADED = True
    except Exception as e:
        print(f"⚠️  Could not preload {DEFAULT_MODEL} model: {str(e)}")
        print("   Model will be loaded on demand")
        MODEL_PRELOADED = False

except ImportError:
    DEEPFACE_AVAILABLE = False
    PRELOADED_MODELS = {}
    MODEL_PRELOADED = False
    AVAILABLE_MODELS = {}
    DEFAULT_MODEL = None
    print("❌ DeepFace not available")


class DeepFaceService:
    """Simple DeepFace service using file-based approach"""
    
    def __init__(self):
        self.available = DEEPFACE_AVAILABLE
        self.model_preloaded = MODEL_PRELOADED
        self.preloaded_model = PRELOADED_MODEL
        self.face_db_path = os.path.join(settings.MEDIA_ROOT, 'face_db')

        # Create face database directory
        os.makedirs(self.face_db_path, exist_ok=True)

        # Ensure weights directory exists and is persistent
        self.ensure_weights_directory()

        if self.available:
            self.model_name = 'VGG-Face'  # Can be changed to 'Facenet', 'OpenFace', etc.
            print(f"✅ DeepFace service initialized with {self.model_name}")
            if self.model_preloaded:
                print("✅ Using preloaded model for better performance")
            else:
                print("⚠️  Model will be loaded on demand")
        else:
            print("❌ DeepFace service not available")

    def ensure_weights_directory(self):
        """Ensure DeepFace weights directory exists and is persistent"""
        import os
        from pathlib import Path

        # DeepFace weights directory
        weights_dir = Path.home() / '.deepface' / 'weights'
        weights_dir.mkdir(parents=True, exist_ok=True)

        print(f"✅ DeepFace weights directory: {weights_dir}")

        # Check if VGG-Face weights exist
        vgg_weights = weights_dir / 'vgg_face_weights.h5'
        if vgg_weights.exists():
            print(f"✅ VGG-Face weights found: {vgg_weights}")
        else:
            print(f"⚠️  VGG-Face weights not found, will download on first use")

        return weights_dir
    
    def preprocess_image(self, image_data):
        """Convert base64 image data to PIL Image"""
        try:
            if isinstance(image_data, str):
                # Handle base64 encoded image
                if image_data.startswith('data:image'):
                    image_data = image_data.split(',')[1]
                
                image_bytes = base64.b64decode(image_data)
                image = Image.open(BytesIO(image_bytes))
            else:
                # Handle file upload
                image = Image.open(image_data)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            return image
        except Exception as e:
            logger.error(f"Error preprocessing image: {str(e)}")
            raise ValueError("Invalid image data")
    
    def enroll_student_face(self, student, image_data, is_primary=False):
        """
        Enroll student face using DeepFace file-based approach
        """
        try:
            # Import here to avoid circular imports
            from .models import FaceEncoding
            
            print(f"🎯 Enrolling face for student: {student.student_id}")
            
            # Preprocess image
            image = self.preprocess_image(image_data)
            print("✅ Image preprocessed successfully")
            
            # Create student directory in face_db
            student_face_dir = os.path.join(self.face_db_path, student.student_id)
            os.makedirs(student_face_dir, exist_ok=True)
            print(f"✅ Created directory: {student_face_dir}")
            
            # Save image to buffer
            img_buffer = BytesIO()
            image.save(img_buffer, format='JPEG')
            img_content = ContentFile(img_buffer.getvalue())
            
            # Create FaceEncoding record
            face_encoding = FaceEncoding.objects.create(
                student=student,
                encoding_data="file_based",  # We use file-based approach
                is_primary=is_primary
            )
            print(f"✅ Created FaceEncoding record: {face_encoding.id}")
            
            # Save image to Django media
            filename = f"{student.student_id}_{face_encoding.id}.jpg"
            face_encoding.image.save(filename, img_content)
            print(f"✅ Saved to Django media: {filename}")
            
            # Save image to face_db directory for DeepFace
            face_db_filename = f"face_{face_encoding.id}.jpg"
            face_db_filepath = os.path.join(student_face_dir, face_db_filename)
            
            # Reset buffer position
            img_buffer.seek(0)
            with open(face_db_filepath, 'wb') as f:
                f.write(img_buffer.getvalue())
            
            print(f"✅ Saved to face_db: {face_db_filepath}")
            
            # Test if DeepFace can read the image and ensure model is loaded
            if self.available:
                try:
                    # Try to analyze the saved image to ensure model works
                    if self.model_preloaded and self.preloaded_model is not None:
                        print("✅ Using preloaded model for validation")
                        analysis = DeepFace.analyze(
                            img_path=face_db_filepath,
                            actions=['age'],
                            enforce_detection=False,
                            model_name=self.model_name
                        )
                    else:
                        print("⚠️  Loading model on demand for validation")
                        analysis = DeepFace.analyze(
                            img_path=face_db_filepath,
                            actions=['age'],
                            enforce_detection=False
                        )
                    print(f"✅ DeepFace can read the image successfully")
                except Exception as e:
                    print(f"⚠️  DeepFace analysis warning: {str(e)}")
                    # Don't fail enrollment if analysis fails, image might still work for recognition
            
            return face_encoding
            
        except Exception as e:
            logger.error(f"Error enrolling student face: {str(e)}")
            print(f"❌ Error enrolling face: {str(e)}")
            raise ValueError(f"Could not enroll face: {str(e)}")
    
    def recognize_student(self, image_data):
        """
        Recognize student using DeepFace.find()
        """
        if not self.available:
            print("❌ DeepFace not available for recognition")
            return None, 0
        
        try:
            print("🔍 Starting face recognition...")
            
            # Preprocess image
            image = self.preprocess_image(image_data)
            
            # Save temporary image for recognition
            temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp')
            os.makedirs(temp_dir, exist_ok=True)
            
            temp_image_path = os.path.join(temp_dir, 'recognition_temp.jpg')
            image.save(temp_image_path, 'JPEG')
            print(f"✅ Saved temp image: {temp_image_path}")
            
            # Use DeepFace.find to search in face database
            print(f"🔍 Searching in face database: {self.face_db_path}")

            # Use preloaded model if available for better performance
            if self.model_preloaded and self.preloaded_model is not None:
                print("✅ Using preloaded model for recognition")
                results = DeepFace.find(
                    img_path=temp_image_path,
                    db_path=self.face_db_path,
                    model_name=self.model_name,
                    model=self.preloaded_model,  # Use preloaded model
                    enforce_detection=False
                )
            else:
                print("⚠️  Loading model on demand")
                results = DeepFace.find(
                    img_path=temp_image_path,
                    db_path=self.face_db_path,
                    model_name=self.model_name,
                    enforce_detection=False
                )
            
            print(f"✅ DeepFace.find completed, results: {len(results)} dataframes")
            
            # Clean up temp file
            try:
                os.remove(temp_image_path)
            except:
                pass
            
            # Process results
            if len(results) > 0 and not results[0].empty:
                # Get the best match (lowest distance)
                best_match = results[0].iloc[0]
                identity_path = best_match['identity']
                distance = best_match['distance']
                
                print(f"✅ Best match: {identity_path}, distance: {distance}")
                
                # Extract student ID from path
                # Path format: face_db/STUDENT_ID/face_X.jpg
                path_parts = identity_path.replace('\\', '/').split('/')
                if len(path_parts) >= 2:
                    student_id = path_parts[-2]  # Get student ID from directory name
                    
                    # Find student
                    from .models import Student
                    try:
                        student = Student.objects.get(student_id=student_id)
                        confidence = max(0, 1 - (distance / 1.0))  # Convert distance to confidence
                        
                        print(f"✅ Recognized student: {student.full_name} (confidence: {confidence:.2f})")
                        return student, confidence
                    except Student.DoesNotExist:
                        print(f"❌ Student {student_id} not found in database")
                        return None, 0
                else:
                    print(f"❌ Could not extract student ID from path: {identity_path}")
                    return None, 0
            else:
                print("❌ No matches found")
                return None, 0
                
        except Exception as e:
            logger.error(f"Error recognizing student: {str(e)}")
            print(f"❌ Recognition error: {str(e)}")
            return None, 0
    
    def validate_image_quality(self, image_data):
        """Basic image validation"""
        try:
            image = self.preprocess_image(image_data)
            
            # Check image size
            width, height = image.size
            if width < 100 or height < 100:
                return False, "Image too small (minimum 100x100 pixels)"
            
            return True, "Image quality is acceptable"
            
        except Exception as e:
            return False, f"Error validating image: {str(e)}"


# Global instance
deepface_service = DeepFaceService()
