"""
Custom authentication backends for the attendance system
"""

from django.contrib.auth.backends import ModelBackend
from django.contrib.auth.models import User
from django.db.models import Q
from .models import *


class EmailBackend(ModelBackend):
    """
    Custom authentication backend that allows users to login with email address
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        Authenticate user with email and password
        """
        if username is None or password is None:
            return None
        
        try:
            # Try to find user by email
            user = CustomUser.objects.get(
                Q(email=username) | Q(username=username)
            )
            
            # Check password
            if user.check_password(password):
                return user
                
        except CustomUser.DoesNotExist:
            # Run the default password hasher once to reduce the timing
            # difference between an existing and a nonexistent user
            CustomUser().set_password(password)
            return None
        
        return None
    
    def get_user(self, user_id):
        """
        Get user by ID
        """
        try:
            return CustomUser.objects.get(pk=user_id)
        except CustomUser.DoesNotExist:
            return None
