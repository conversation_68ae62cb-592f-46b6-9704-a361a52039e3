
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard - AI Attendance System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .class-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
        }
        
        .class-card:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .attendance-status {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.2rem;
        }
        
        .btn-attendance {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 10px;
            transition: all 0.2s ease;
        }
        
        .btn-attendance:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-align: center;
            padding: 1.5rem;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-user-graduate me-2"></i>Student Portal
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>{{ student.full_name }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#profileModal">
                            <i class="fas fa-user me-2"></i>Profile
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{% url 'student_logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- Welcome Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card welcome-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="card-title mb-2">
                                    <i class="fas fa-sun me-2"></i>Welcome back, {{ student.first_name }}!
                                </h2>
                                <p class="card-text mb-0">
                                    Ready to mark your attendance? Select a class below and use face recognition.
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="stats-card rounded">
                                    <h4 class="mb-1">{{ today|date:"M d, Y" }}</h4>
                                    <small>Today's Date</small>
                                </div>
                            </div>
                        </div>

                        <!-- Face Registration Status -->
                        {% if student.face_encodings.count == 0 %}
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>Face Not Registered!</strong> You need to register your face to mark attendance.
                                        </div>
                                        <a href="{% url 'student_face_enrollment' %}" class="btn btn-warning btn-sm">
                                            <i class="fas fa-camera me-1"></i>Register Face
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Classes Section -->
        <div class="row mb-4">
            <div class="col-12">
                <h3 class="mb-3">
                    <i class="fas fa-chalkboard me-2"></i>Your Classes
                </h3>
            </div>
        </div>

        {% if classes_with_attendance %}
            <div class="row">
                {% for item in classes_with_attendance %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card class-card position-relative" onclick="markAttendance({{ item.class.id }})">
                        <!-- Attendance Status Icon -->
                        <div class="attendance-status">
                            {% if item.is_marked %}
                                {% if item.attendance.status == 'present' %}
                                    <i class="fas fa-check-circle text-success" title="Present"></i>
                                {% elif item.attendance.status == 'late' %}
                                    <i class="fas fa-clock text-warning" title="Late"></i>
                                {% else %}
                                    <i class="fas fa-times-circle text-danger" title="Absent"></i>
                                {% endif %}
                            {% else %}
                                <i class="fas fa-question-circle text-light" title="Not marked"></i>
                            {% endif %}
                        </div>

                        <div class="card-body">
                            <h5 class="card-title">{{ item.class.code }}</h5>
                            <h6 class="card-subtitle mb-3 opacity-75">{{ item.class.name }}</h6>

                            {% if item.class.description %}
                                <p class="card-text small opacity-75">{{ item.class.description|truncatewords:10 }}</p>
                            {% endif %}

                            <div class="mb-3">
                                <small class="opacity-75">
                                    <i class="fas fa-user-tie me-1"></i>
                                    {{ item.class.instructor.get_full_name|default:item.class.instructor.username }}
                                </small>
                            </div>

                            {% if item.is_marked %}
                                <div class="alert alert-success mb-3" style="background: rgba(255, 255, 255, 0.2); border: none;">
                                    <small>
                                        <i class="fas fa-check me-1"></i>
                                        Attendance marked at {{ item.attendance.timestamp|time:"H:i" }}
                                    </small>
                                </div>
                                <div class="btn btn-attendance w-100" style="background: rgba(40, 167, 69, 0.8);">
                                    <i class="fas fa-check me-2"></i>Attended
                                </div>
                            {% else %}
                                <div class="btn btn-attendance w-100" style="background: rgba(255, 193, 7, 0.8);">
                                    <i class="fas fa-clock me-2"></i>Pending
                                </div>
                                <small class="text-white-50 mt-2 d-block">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Use face recognition in class with your lecturer
                                </small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-chalkboard fa-3x text-muted mb-4"></i>
                            <h4 class="text-muted mb-3">No Classes Enrolled</h4>
                            <p class="text-muted mb-4">You are not enrolled in any classes yet.</p>

                            <div class="alert alert-info text-start">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>How to get enrolled:
                                </h6>
                                <ol class="mb-0">
                                    <li><strong>Contact your instructor</strong> - Ask them to enroll you in their classes</li>
                                    <li><strong>Visit the admin office</strong> - They can enroll you in the appropriate classes</li>
                                    <li><strong>Check with your academic advisor</strong> - They can help with course registration</li>
                                </ol>
                            </div>

                            <div class="mt-4">
                                <p class="small text-muted">
                                    <i class="fas fa-user-tie me-1"></i>
                                    Student ID: <strong>{{ student.student_id }}</strong> |
                                    Email: <strong>{{ student.email }}</strong>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Recent Attendance -->
        {% if recent_attendance %}
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="mb-3">
                    <i class="fas fa-history me-2"></i>Recent Attendance
                </h3>
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Class</th>
                                        <th>Date</th>
                                        <th>Time</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in recent_attendance %}
                                    <tr>
                                        <td>
                                            <strong>{{ record.class_attended.code }}</strong><br>
                                            <small class="text-muted">{{ record.class_attended.name }}</small>
                                        </td>
                                        <td>{{ record.date|date:"M d, Y" }}</td>
                                        <td>{{ record.timestamp|time:"H:i" }}</td>
                                        <td>
                                            {% if record.status == 'present' %}
                                                <span class="badge bg-success">Present</span>
                                            {% elif record.status == 'late' %}
                                                <span class="badge bg-warning">Late</span>
                                            {% else %}
                                                <span class="badge bg-danger">Absent</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Profile Modal -->
    <div class="modal fade" id="profileModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user me-2"></i>Student Profile
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Student ID:</strong></td>
                            <td>{{ student.student_id }}</td>
                        </tr>
                        <tr>
                            <td><strong>Name:</strong></td>
                            <td>{{ student.full_name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Email:</strong></td>
                            <td>{{ student.email }}</td>
                        </tr>
                        <tr>
                            <td><strong>Phone:</strong></td>
                            <td>{{ student.phone|default:"Not provided" }}</td>
                        </tr>
                        <tr>
                            <td><strong>Enrolled Classes:</strong></td>
                            <td>{{ enrolled_classes|length }}</td>
                        </tr>
                        <tr>
                            <td><strong>Face Enrolled:</strong></td>
                            <td>
                                {% if student.face_encodings.count > 0 %}
                                    <span class="badge bg-success">Yes</span>
                                {% else %}
                                    <span class="badge bg-warning">No</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function markAttendance(classId) {
            // Check if attendance already marked
            const card = event.currentTarget;
            const button = card.querySelector('button');

            if (button.disabled) {
                return; // Already marked
            }

            // Check if face is registered
            {% if student.face_encodings.count == 0 %}
                if (confirm('You need to register your face first. Would you like to register now?')) {
                    window.location.href = '{% url "student_face_enrollment" %}';
                }
                return;
            {% endif %}

            // Redirect to attendance marking page
            window.location.href = `/mark-attendance/${classId}/`;
        }
        
        // Add template filter for dictionary lookup
        function lookup(dict, key) {
            return dict[key];
        }
    </script>
</body>
</html>
