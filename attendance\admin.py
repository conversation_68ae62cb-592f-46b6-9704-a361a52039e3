from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Student, Class, Attendance, FaceEncoding, AttendanceSession


@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ['student_id', 'full_name', 'email', 'enrolled_classes_count', 'face_encodings_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at', 'enrolled_classes']
    search_fields = ['student_id', 'first_name', 'last_name', 'email']
    readonly_fields = ['created_at']
    filter_horizontal = ['enrolled_classes']

    def enrolled_classes_count(self, obj):
        return obj.enrolled_classes.count()
    enrolled_classes_count.short_description = 'Classes'

    def face_encodings_count(self, obj):
        count = obj.face_encodings.count()
        if count > 0:
            url = reverse('admin:attendance_faceencoding_changelist') + f'?student__id__exact={obj.id}'
            return format_html('<a href="{}">{} encodings</a>', url, count)
        return '0 encodings'
    face_encodings_count.short_description = 'Face Encodings'

    def full_name(self, obj):
        return obj.full_name
    full_name.short_description = 'Full Name'


@admin.register(Class)
class ClassAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'instructor', 'students_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at', 'instructor']
    search_fields = ['code', 'name', 'description']
    readonly_fields = ['created_at']

    def students_count(self, obj):
        return obj.students.count()
    students_count.short_description = 'Students'


@admin.register(FaceEncoding)
class FaceEncodingAdmin(admin.ModelAdmin):
    list_display = ['student', 'is_primary', 'image_preview', 'created_at']
    list_filter = ['is_primary', 'created_at']
    search_fields = ['student__student_id', 'student__first_name', 'student__last_name']
    readonly_fields = ['created_at', 'encoding_data', 'image_preview']

    def image_preview(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="50" height="50" style="object-fit: cover;" />', obj.image.url)
        return 'No image'
    image_preview.short_description = 'Preview'


@admin.register(Attendance)
class AttendanceAdmin(admin.ModelAdmin):
    list_display = ['student', 'class_attended', 'date', 'status', 'recognition_confidence', 'marked_by_recognition', 'timestamp']
    list_filter = ['status', 'marked_by_recognition', 'date', 'class_attended']
    search_fields = ['student__student_id', 'student__first_name', 'student__last_name', 'class_attended__code']
    readonly_fields = ['timestamp']
    date_hierarchy = 'date'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('student', 'class_attended')


@admin.register(AttendanceSession)
class AttendanceSessionAdmin(admin.ModelAdmin):
    list_display = ['class_session', 'date', 'attendance_rate_display', 'present_count', 'total_students', 'is_active', 'start_time']
    list_filter = ['is_active', 'date', 'class_session']
    search_fields = ['class_session__code', 'class_session__name']
    readonly_fields = ['start_time', 'end_time', 'attendance_rate']
    date_hierarchy = 'date'

    def attendance_rate_display(self, obj):
        rate = obj.attendance_rate
        if rate >= 80:
            color = 'green'
        elif rate >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html('<span style="color: {};">{:.1f}%</span>', color, rate)
    attendance_rate_display.short_description = 'Attendance Rate'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('class_session', 'created_by')


# Customize admin site
admin.site.site_header = 'AI Attendance System Administration'
admin.site.site_title = 'AI Attendance Admin'
admin.site.index_title = 'Welcome to AI Attendance System Administration'
