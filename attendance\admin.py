from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db import transaction
from .models import Student, Class, Attendance, FaceEncoding, AttendanceSession


# Unregister the default User admin
admin.site.unregister(User)


@admin.register(User)
class CustomUserAdmin(BaseUserAdmin):
    """Custom User admin with position management"""

    list_display = ['username', 'email', 'first_name', 'last_name', 'position_display', 'is_active', 'date_joined']
    list_filter = ['is_staff', 'is_active', 'date_joined']
    search_fields = ['username', 'email', 'first_name', 'last_name']

    # Add position field to the form
    fieldsets = BaseUserAdmin.fieldsets + (
        ('Position', {
            'fields': ('position_info',),
            'description': 'User position in the system'
        }),
    )

    readonly_fields = ['position_info']

    def position_display(self, obj):
        """Display user position with color coding"""
        if obj.is_staff:
            return format_html('<span style="color: blue; font-weight: bold;">👨‍🏫 Lecturer</span>')
        else:
            # Check if user has a student record
            try:
                student = Student.objects.get(student_id=obj.username)
                return format_html('<span style="color: green; font-weight: bold;">🎓 Student</span>')
            except Student.DoesNotExist:
                return format_html('<span style="color: orange;">👤 User (No Student Record)</span>')

    position_display.short_description = 'Position'

    def position_info(self, obj):
        """Show detailed position information"""
        if obj.is_staff:
            # Count classes taught
            classes_count = Class.objects.filter(instructor=obj).count()
            return format_html(
                '<div style="background: #e3f2fd; padding: 10px; border-radius: 5px;">'
                '<strong>👨‍🏫 Lecturer</strong><br>'
                'Classes taught: {}<br>'
                '<small>This user can access the lecturer dashboard and manage classes.</small>'
                '</div>',
                classes_count
            )
        else:
            try:
                student = Student.objects.get(student_id=obj.username)
                classes_count = student.enrolled_classes.count()
                face_count = student.face_encodings.count()
                return format_html(
                    '<div style="background: #e8f5e8; padding: 10px; border-radius: 5px;">'
                    '<strong>🎓 Student</strong><br>'
                    'Student ID: {}<br>'
                    'Enrolled classes: {}<br>'
                    'Face encodings: {}<br>'
                    '<small>This user can access the student dashboard and mark attendance.</small>'
                    '</div>',
                    student.student_id, classes_count, face_count
                )
            except Student.DoesNotExist:
                return format_html(
                    '<div style="background: #fff3e0; padding: 10px; border-radius: 5px;">'
                    '<strong>👤 Regular User</strong><br>'
                    '<small>No student record found. Create a student record to enable attendance features.</small><br>'
                    '<a href="/admin/attendance/student/add/?student_id={}" target="_blank">Create Student Record</a>'
                    '</div>',
                    obj.username
                )

    position_info.short_description = 'Position Details'

    def save_model(self, request, obj, form, change):
        """Custom save logic"""
        super().save_model(request, obj, form, change)

        # If this is a new user or email changed, update student record if exists
        if hasattr(obj, 'username'):
            try:
                student = Student.objects.get(student_id=obj.username)
                if student.email != obj.email:
                    student.email = obj.email
                    student.first_name = obj.first_name
                    student.last_name = obj.last_name
                    student.save()
            except Student.DoesNotExist:
                pass

    actions = ['make_lecturer', 'make_student', 'create_student_record']

    def make_lecturer(self, request, queryset):
        """Make selected users lecturers"""
        updated = queryset.update(is_staff=True)
        self.message_user(request, f'{updated} users were successfully made lecturers.')
    make_lecturer.short_description = "Make selected users lecturers"

    def make_student(self, request, queryset):
        """Remove lecturer privileges from selected users"""
        updated = queryset.update(is_staff=False)
        self.message_user(request, f'{updated} users were successfully made students.')
    make_student.short_description = "Remove lecturer privileges"

    def create_student_record(self, request, queryset):
        """Create student records for selected users"""
        created_count = 0
        for user in queryset:
            if not user.is_staff:  # Only create for non-staff users
                student, created = Student.objects.get_or_create(
                    student_id=user.username,
                    defaults={
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'email': user.email,
                        'is_active': True
                    }
                )
                if created:
                    created_count += 1

        self.message_user(request, f'{created_count} student records were created.')
    create_student_record.short_description = "Create student records for selected users"




@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ['student_id', 'full_name', 'email', 'enrolled_classes_count', 'face_encodings_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at', 'enrolled_classes']
    search_fields = ['student_id', 'first_name', 'last_name', 'email']
    readonly_fields = ['created_at']
    filter_horizontal = ['enrolled_classes']

    def enrolled_classes_count(self, obj):
        return obj.enrolled_classes.count()
    enrolled_classes_count.short_description = 'Classes'

    def face_encodings_count(self, obj):
        count = obj.face_encodings.count()
        if count > 0:
            url = reverse('admin:attendance_faceencoding_changelist') + f'?student__id__exact={obj.id}'
            return format_html('<a href="{}">{} encodings</a>', url, count)
        return '0 encodings'
    face_encodings_count.short_description = 'Face Encodings'

    def full_name(self, obj):
        return obj.full_name
    full_name.short_description = 'Full Name'


@admin.register(Class)
class ClassAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'instructor', 'students_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at', 'instructor']
    search_fields = ['code', 'name', 'description']
    readonly_fields = ['created_at']

    def students_count(self, obj):
        return obj.students.count()
    students_count.short_description = 'Students'


@admin.register(FaceEncoding)
class FaceEncodingAdmin(admin.ModelAdmin):
    list_display = ['student', 'is_primary', 'image_preview', 'created_at']
    list_filter = ['is_primary', 'created_at']
    search_fields = ['student__student_id', 'student__first_name', 'student__last_name']
    readonly_fields = ['created_at', 'encoding_data', 'image_preview']

    def image_preview(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="50" height="50" style="object-fit: cover;" />', obj.image.url)
        return 'No image'
    image_preview.short_description = 'Preview'


@admin.register(Attendance)
class AttendanceAdmin(admin.ModelAdmin):
    list_display = ['student', 'class_attended', 'date', 'status', 'recognition_confidence', 'marked_by_recognition', 'timestamp']
    list_filter = ['status', 'marked_by_recognition', 'date', 'class_attended']
    search_fields = ['student__student_id', 'student__first_name', 'student__last_name', 'class_attended__code']
    readonly_fields = ['timestamp']
    date_hierarchy = 'date'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('student', 'class_attended')


@admin.register(AttendanceSession)
class AttendanceSessionAdmin(admin.ModelAdmin):
    list_display = ['class_session', 'date', 'attendance_rate_display', 'present_count', 'total_students', 'is_active', 'start_time']
    list_filter = ['is_active', 'date', 'class_session']
    search_fields = ['class_session__code', 'class_session__name']
    readonly_fields = ['start_time', 'end_time', 'attendance_rate']
    date_hierarchy = 'date'

    def attendance_rate_display(self, obj):
        rate = obj.attendance_rate
        if rate >= 80:
            color = 'green'
        elif rate >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html('<span style="color: {};">{:.1f}%</span>', color, rate)
    attendance_rate_display.short_description = 'Attendance Rate'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('class_session', 'created_by')


# Customize admin site
admin.site.site_header = 'AI Attendance System Administration'
admin.site.site_title = 'AI Attendance Admin'
admin.site.index_title = 'Welcome to AI Attendance System Administration'
